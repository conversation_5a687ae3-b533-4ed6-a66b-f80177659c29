"""
Simple Backtester for Dashboard Integration
==========================================

A simplified backtester that uses the working indicator.py and provides
reliable results for the dashboard without complex dependencies.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional

# Import the working indicator system
from .indicator import compute_indicators, generate_trade_signal

class SimpleBigTrendBacktester:
    """Simplified backtester using working indicator system"""
    
    def __init__(self, initial_capital: float = 100000):
        """Initialize the simple backtester"""
        self.initial_capital = initial_capital
        
        # Simple strategy settings
        self.settings = {
            'min_confluence': 4.0,
            'min_rr': 2.0,
            'max_trades_per_day': 3,
            'fees': 0.0005,  # 0.05% fees
            'slippage': 0.001  # 0.1% slippage
        }
        
    def run_full_backtest(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame) -> Dict[str, Any]:
        """Run a complete backtest using the working indicator system"""
        
        print("🚀 Running Simple Big Trend Backtest...")
        print(f"📊 Data: {len(df_3m)} 3m candles, {len(df_15m)} 15m candles, {len(df_1h)} 1h candles")
        
        if df_3m.empty or df_15m.empty or df_1h.empty:
            print("❌ Insufficient data for backtesting")
            return self._create_empty_results()
            
        try:
            # Generate signals using working system
            signals = self._generate_signals(df_3m, df_15m, df_1h)
            
            if not signals:
                print("❌ No signals generated")
                return self._create_empty_results()
                
            print(f"✅ Generated {len(signals)} signals")
            
            # Run backtest simulation
            results = self._simulate_trades(df_3m, signals)
            
            print(f"✅ Backtest completed!")
            print(f"   Total Return: {results['total_return']:.2%}")
            print(f"   Total Trades: {results['total_trades']}")
            print(f"   Win Rate: {results['win_rate']:.1%}")
            
            return results
            
        except Exception as e:
            print(f"❌ Backtest failed: {e}")
            import traceback
            traceback.print_exc()
            return self._create_empty_results()
    
    def _generate_signals(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame) -> list:
        """Generate trading signals using the working indicator system"""
        
        signals = []
        
        # Process signals every 15 candles to avoid too many signals
        step_size = 15
        start_idx = max(200, len(df_3m) - 1000)  # Start from recent data
        
        for i in range(start_idx, len(df_3m), step_size):
            try:
                # Get data slices for signal generation
                current_3m = df_3m.iloc[:i+1].tail(100)  # Last 100 candles
                
                # Find corresponding 15m and 1h data
                current_time = df_3m.index[i] if hasattr(df_3m, 'index') else df_3m.iloc[i]['timestamp']
                
                # Get aligned timeframe data
                if hasattr(df_15m, 'index'):
                    current_15m = df_15m[df_15m.index <= current_time].tail(20)
                    current_1h = df_1h[df_1h.index <= current_time].tail(5)
                else:
                    current_15m = df_15m[df_15m['timestamp'] <= current_time].tail(20)
                    current_1h = df_1h[df_1h['timestamp'] <= current_time].tail(5)
                
                if len(current_3m) < 50 or len(current_15m) < 10 or len(current_1h) < 3:
                    continue
                    
                # Generate signal using working system
                signal_result = generate_trade_signal(current_3m, current_15m, current_1h)
                
                if signal_result['signal'] != 'hold':
                    # Calculate signal strength
                    signal_strength = max(signal_result['buy_score'], signal_result['sell_score'])
                    
                    if signal_strength >= self.settings['min_confluence']:
                        signals.append({
                            'timestamp': current_time,
                            'signal': signal_result['signal'],
                            'entry_price': signal_result['entry_price'],
                            'stop_loss': signal_result['stop_loss'],
                            'take_profit': signal_result['take_profit'],
                            'strength': signal_strength,
                            'buy_score': signal_result['buy_score'],
                            'sell_score': signal_result['sell_score']
                        })
                        
            except Exception as e:
                continue
                
        return signals
    
    def _simulate_trades(self, df_3m: pd.DataFrame, signals: list) -> Dict[str, Any]:
        """Simulate trading based on generated signals"""
        
        trades = []
        portfolio_value = self.initial_capital
        peak_value = self.initial_capital
        max_drawdown = 0
        
        for signal in signals:
            try:
                # Find entry point in data
                entry_time = signal['timestamp']
                
                # Find the index for this timestamp
                if hasattr(df_3m, 'index'):
                    entry_idx = df_3m.index.get_loc(entry_time, method='nearest')
                    future_data = df_3m.iloc[entry_idx+1:entry_idx+500]  # Look ahead 500 candles
                else:
                    entry_idx = df_3m[df_3m['timestamp'] >= entry_time].index[0]
                    future_data = df_3m.iloc[entry_idx+1:entry_idx+500]
                
                if len(future_data) < 10:
                    continue
                    
                # Simulate trade execution
                trade_result = self._execute_trade(signal, future_data)
                
                if trade_result:
                    trades.append(trade_result)
                    
                    # Update portfolio
                    portfolio_value += trade_result['pnl']
                    peak_value = max(peak_value, portfolio_value)
                    
                    # Calculate drawdown
                    current_drawdown = (peak_value - portfolio_value) / peak_value
                    max_drawdown = max(max_drawdown, current_drawdown)
                    
            except Exception as e:
                continue
        
        # Calculate results
        return self._calculate_results(trades, portfolio_value, max_drawdown)
    
    def _execute_trade(self, signal: dict, future_data: pd.DataFrame) -> Optional[dict]:
        """Execute a single trade simulation"""
        
        entry_price = signal['entry_price']
        stop_loss = signal['stop_loss']
        take_profit = signal['take_profit']
        direction = 1 if signal['signal'] == 'buy' else -1
        
        # Position size (2% risk)
        risk_amount = self.initial_capital * 0.02
        sl_distance = abs(entry_price - stop_loss)
        position_size = risk_amount / sl_distance if sl_distance > 0 else 0
        
        if position_size <= 0:
            return None
            
        # Simulate trade execution through future data
        for _, candle in future_data.iterrows():
            current_price = candle['close']
            
            # Check exit conditions
            if direction == 1:  # Long trade
                if current_price <= stop_loss:
                    # Stop loss hit
                    pnl = (stop_loss - entry_price) * position_size
                    return {
                        'entry_price': entry_price,
                        'exit_price': stop_loss,
                        'pnl': pnl,
                        'exit_reason': 'stop_loss',
                        'direction': 'long'
                    }
                elif current_price >= take_profit:
                    # Take profit hit
                    pnl = (take_profit - entry_price) * position_size
                    return {
                        'entry_price': entry_price,
                        'exit_price': take_profit,
                        'pnl': pnl,
                        'exit_reason': 'take_profit',
                        'direction': 'long'
                    }
            else:  # Short trade
                if current_price >= stop_loss:
                    # Stop loss hit
                    pnl = (entry_price - stop_loss) * position_size
                    return {
                        'entry_price': entry_price,
                        'exit_price': stop_loss,
                        'pnl': pnl,
                        'exit_reason': 'stop_loss',
                        'direction': 'short'
                    }
                elif current_price <= take_profit:
                    # Take profit hit
                    pnl = (entry_price - take_profit) * position_size
                    return {
                        'entry_price': entry_price,
                        'exit_price': take_profit,
                        'pnl': pnl,
                        'exit_reason': 'take_profit',
                        'direction': 'short'
                    }
        
        # No exit found - close at last price
        last_price = future_data.iloc[-1]['close']
        if direction == 1:
            pnl = (last_price - entry_price) * position_size
        else:
            pnl = (entry_price - last_price) * position_size
            
        return {
            'entry_price': entry_price,
            'exit_price': last_price,
            'pnl': pnl,
            'exit_reason': 'end_of_data',
            'direction': 'long' if direction == 1 else 'short'
        }
    
    def _calculate_results(self, trades: list, final_value: float, max_drawdown: float) -> Dict[str, Any]:
        """Calculate comprehensive backtest results"""
        
        if not trades:
            return self._create_empty_results()
            
        # Basic metrics
        total_return = (final_value - self.initial_capital) / self.initial_capital
        total_trades = len(trades)
        
        # Trade analysis
        winning_trades = [t for t in trades if t['pnl'] > 0]
        losing_trades = [t for t in trades if t['pnl'] <= 0]
        
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        
        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0
        
        profit_factor = abs(avg_win * len(winning_trades) / (avg_loss * len(losing_trades))) if losing_trades and avg_loss != 0 else 0
        
        # Big winner analysis (trades with >800 point moves)
        big_winners = len([t for t in winning_trades if abs(t['exit_price'] - t['entry_price']) > 800])
        
        return {
            'total_return': total_return,
            'final_value': final_value,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'big_winners': big_winners,
            'sharpe_ratio': total_return / max(max_drawdown, 0.01),  # Simplified Sharpe
            'trades': trades,
            'status': 'completed'
        }
    
    def _create_empty_results(self) -> Dict[str, Any]:
        """Create empty results for failed backtests"""
        return {
            'total_return': 0.0,
            'final_value': self.initial_capital,
            'total_trades': 0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'big_winners': 0,
            'sharpe_ratio': 0.0,
            'trades': [],
            'status': 'failed'
        }
