#!/usr/bin/env python3
"""
Simple Backtest Test
===================

Test the specific error we encountered: 'supertrend' KeyError
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def create_sample_data():
    """Create sample OHLCV data for testing"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='3min')
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 50000
    prices = base_price + np.cumsum(np.random.normal(0, 100, 100))
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices + np.random.normal(0, 50, 100),
        'high': prices + np.abs(np.random.normal(100, 50, 100)),
        'low': prices - np.abs(np.random.normal(100, 50, 100)),
        'close': prices,
        'volume': np.random.uniform(100, 1000, 100)
    })

    # Keep timestamp as column for indicator function compatibility

    return df

def test_indicator_computation():
    """Test the indicator computation that was failing"""
    
    print("🧪 Testing Indicator Computation...")
    print("=" * 40)
    
    try:
        # Create sample data
        df_3m = create_sample_data()
        df_15m = df_3m.iloc[::5].copy()  # Downsample
        df_1h = df_3m.iloc[::20].copy()  # Downsample
        
        print(f"✅ Sample data created:")
        print(f"   3m: {len(df_3m)} candles")
        print(f"   15m: {len(df_15m)} candles") 
        print(f"   1h: {len(df_1h)} candles")
        
        # Test the working indicator function
        print("\n🔍 Testing working indicator function...")
        from crypto_market.engines.Trading_engine.indicator import compute_indicators
        
        result_df = compute_indicators(df_3m)
        print(f"✅ Working indicators computed successfully!")
        print(f"   Columns: {list(result_df.columns)}")
        
        # Test the enhanced indicator function that was failing
        print("\n🔍 Testing enhanced indicator function...")
        from crypto_market.engines.Trading_engine.backtester_vbt import compute_enhanced_indicators
        
        enhanced_df = compute_enhanced_indicators(df_3m)
        print(f"✅ Enhanced indicators computed successfully!")
        print(f"   Columns: {list(enhanced_df.columns)}")
        
        # Test signal generation
        print("\n🎯 Testing signal generation...")
        from crypto_market.engines.Trading_engine.backtester_vbt import generate_enhanced_signals
        
        signal = generate_enhanced_signals(df_3m, df_15m, df_1h)
        if signal:
            print(f"✅ Signal generated:")
            print(f"   Direction: {signal.signal}")
            print(f"   Entry: ${signal.entry_price:,.2f}")
            print(f"   Stop Loss: ${signal.stop_loss:,.2f}")
            print(f"   Take Profit: ${signal.take_profit:,.2f}")
        else:
            print("ℹ️ No signal generated (normal for random data)")
            
        # Test backtester initialization
        print("\n🚀 Testing backtester initialization...")
        from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        
        backtester = BigTrendVectorBTBacktester(initial_capital=100000)
        print("✅ Backtester initialized successfully!")
        
        # Test data preparation (the step that was failing)
        print("\n📊 Testing data preparation...")
        df_3m_ind, df_15m_ind, df_1h_ind = backtester.prepare_data(df_3m, df_15m, df_1h)
        
        print(f"✅ Data preparation successful!")
        print(f"   3m with indicators: {len(df_3m_ind)} rows")
        print(f"   15m with indicators: {len(df_15m_ind)} rows")
        print(f"   1h with indicators: {len(df_1h_ind)} rows")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The supertrend KeyError has been fixed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        print("\n🔍 Error details:")
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_indicator_computation()
    if success:
        print("\n🚀 Backtest integration is ready!")
        print("💡 You can now run backtests from the dashboard!")
    else:
        print("\n🔧 Please check the error above")
