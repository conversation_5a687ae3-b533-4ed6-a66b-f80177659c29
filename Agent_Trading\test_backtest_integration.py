#!/usr/bin/env python3
"""
Test Backtest Integration
========================

Quick test to verify the backtesting system works properly
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root.parent))  # Add parent for core module

def test_backtest_integration():
    """Test the complete backtest integration"""
    
    print("🧪 Testing Backtest Integration...")
    print("=" * 50)
    
    try:
        # Test 1: Import components
        print("📦 Testing imports...")
        from core.config_manager import ConfigManager
        from crypto_market.engines.data_engine.database_manager import DatabaseManager
        from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        print("✅ All imports successful!")
        
        # Test 2: Initialize components
        print("\n🔧 Testing component initialization...")
        config_path = project_root / "config.json"
        config = ConfigManager(str(config_path))
        db_manager = DatabaseManager(config)
        backtester = BigTrendVectorBTBacktester(initial_capital=100000)
        print("✅ Components initialized successfully!")
        
        # Test 3: Load data
        print("\n📊 Testing data loading...")
        df_3m = db_manager.get_market_data(timeframe='3m', limit=1000)
        df_15m = db_manager.get_market_data(timeframe='15m', limit=200)
        df_1h = db_manager.get_market_data(timeframe='1h', limit=50)
        
        print(f"✅ Data loaded:")
        print(f"   3m: {len(df_3m)} candles")
        print(f"   15m: {len(df_15m)} candles")
        print(f"   1h: {len(df_1h)} candles")
        
        if df_3m.empty:
            print("⚠️ No data available - please fetch data first")
            return False
            
        # Test 4: Test indicator computation
        print("\n🔍 Testing indicator computation...")
        from crypto_market.engines.Trading_engine.indicator import compute_indicators
        
        # Test with small sample
        test_df = df_3m.head(100).copy()
        indicators_df = compute_indicators(test_df)
        
        print(f"✅ Indicators computed:")
        print(f"   Original columns: {list(test_df.columns)}")
        print(f"   With indicators: {list(indicators_df.columns)}")
        
        # Test 5: Test signal generation
        print("\n🎯 Testing signal generation...")
        from crypto_market.engines.Trading_engine.indicator import generate_trade_signal
        
        # Test signal generation with small datasets
        signal_result = generate_trade_signal(
            df_3m.head(100), 
            df_15m.head(20), 
            df_1h.head(5)
        )
        
        print(f"✅ Signal generated:")
        print(f"   Signal: {signal_result['signal']}")
        print(f"   Entry: ${signal_result['entry_price']:,.2f}")
        print(f"   Buy Score: {signal_result['buy_score']}")
        print(f"   Sell Score: {signal_result['sell_score']}")
        
        # Test 6: Test backtester preparation
        print("\n🚀 Testing backtester data preparation...")
        
        # Use smaller dataset for testing
        test_3m = df_3m.head(500)
        test_15m = df_15m.head(100) 
        test_1h = df_1h.head(25)
        
        # Test the prepare_data method
        df_3m_ind, df_15m_ind, df_1h_ind = backtester.prepare_data(test_3m, test_15m, test_1h)
        
        print(f"✅ Data preparation successful:")
        print(f"   3m indicators: {len(df_3m_ind)} rows")
        print(f"   15m indicators: {len(df_15m_ind)} rows") 
        print(f"   1h indicators: {len(df_1h_ind)} rows")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Backtest integration is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        print("\n🔍 Error details:")
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_backtest_integration()
    if success:
        print("\n🚀 Ready to run backtests!")
    else:
        print("\n🔧 Please fix the issues above before running backtests")
